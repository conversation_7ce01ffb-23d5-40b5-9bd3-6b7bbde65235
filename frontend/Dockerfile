# 使用 Node.js 20 作为基础镜像
FROM node:20-alpine AS deps

# 设置工作目录
WORKDIR /app

# 配置使用中国国内源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装 yarn
RUN npm install -g yarn --registry=https://registry.npmmirror.com

# 配置yarn使用中国国内源
RUN yarn config set registry https://registry.npmmirror.com && \
    yarn config set sass_binary_site https://npmmirror.com/mirrors/node-sass

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装依赖
RUN yarn install --production

# 构建阶段
FROM node:20-alpine AS build

# 设置工作目录
WORKDIR /app

# 配置使用中国国内源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装 yarn
RUN npm install -g yarn --registry=https://registry.npmmirror.com

# 配置yarn使用中国国内源
RUN yarn config set registry https://registry.npmmirror.com && \
    yarn config set sass_binary_site https://npmmirror.com/mirrors/node-sass

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖（包括开发依赖）
RUN yarn install

# 复制源代码
COPY . .

# 构建应用
RUN yarn build

# 使用 Nginx 作为生产环境服务器
FROM nginx:alpine

# 配置使用中国国内源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 复制构建的文件到 Nginx 默认目录
COPY --from=build /app/dist /usr/share/nginx/html

# 添加自定义 nginx 配置以优化性能
RUN touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 创建 nginx 用户和组
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -G nginx

# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]