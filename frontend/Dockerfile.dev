# 使用 Node.js 20 作为基础镜像
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 配置使用中国国内源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装 yarn
RUN npm install -g yarn --registry=https://registry.npmmirror.com

# 配置yarn使用中国国内源
RUN yarn config set registry https://registry.npmmirror.com && \
    yarn config set sass_binary_site https://npmmirror.com/mirrors/node-sass

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖（包括开发依赖）
RUN yarn install

# 暴露端口
EXPOSE 5173

# 启动开发服务器
CMD ["yarn", "dev", "--", "--host", "0.0.0.0"]